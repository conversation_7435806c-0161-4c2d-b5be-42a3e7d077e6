# pyAHC项目HDF5数据同化集成方案

## 项目升级目标

将pyAHC项目改造升级为支持数据同化的水文模型框架，基于科学合理的HDF5存储架构，重点关注：

1. **数据管理架构升级** - 使用HDF5实现高效的时间序列状态变量存储和传递
2. **模型接口标准化** - 提供健壮的、配置驱动的状态变量提取和设置接口
3. **工作流框架建设** - 支持逐日连续模拟、状态传递和错误恢复
4. **数据同化接口标准化** - 为外部数据同化算法提供标准化、实用的接口
5. **性能优化保障** - 实现增量读写、数据压缩和并发访问控制
6. **可靠性增强** - 提供数据完整性检查和错误恢复机制

## 架构设计原则

**独立实现策略**：基于评估结果，本方案采用独立实现而非扩展现有HDF5类，原因：
- 现有HDF5类功能有限，仅支持基本模型存储
- 项目中无实际使用记录，缺乏验证
- 数据同化需求与现有架构不匹配
- 独立实现可确保功能完整性和可靠性


## 1. 环境准备

### 1.1 确认依赖
检查 `pyproject.toml` 中已包含HDF5依赖：
```toml
h5py = "^3.11.0"
```

### 1.2 安装依赖
```bash
cd pyAHC
poetry install
# 或者
pip install h5py
```

## 2. HDF5数据组织结构设计

### 2.1 标准化数据组织结构

```
project.h5
├── corn_001-2013/                    # 项目组（玉米地块001-2013年）
│   ├── day_05-01/                    # 日期组（5月1日）
│   │   ├── input/                    # 模型输入(pickled Model对象)
│   │   ├── output/                   # 模型输出(pickled Result对象)
│   │   └── state-parameter_variables/ # 状态-参数变量
│   │       ├── soil_moisture         # 土壤含水量（状态变量）
│   │       ├── lai                   # 叶面积指数（状态变量）
│   │       ├── biomass               # 生物量（状态变量）
│   │       ├── root_depth            # 根深（状态变量）
│   │       ├── groundwater_level     # 地下水位（状态变量）
│   │       ├── param_hydraulic_conductivity # 土壤导水率（参数变量）
│   │       ├── param_crop_coefficient       # 作物系数（参数变量）
│   │       └── assimilated_*         # 数据同化后的变量
│   ├── day_05-02/                    # 5月2日
│   │   ├── input/                    # 包含前一天的状态-参数变量
│   │   ├── output/                   # 当天模拟结果
│   │   └── state-parameter_variables/ # 状态-参数变量
│   └── day_05-03/                    # 5月3日
│       └── ...                       # 类似结构
├── corn_002-2013/                    # 其他地块项目
│   └── ...
└── wheat_001-2014/                   # 其他作物项目
    └── ...
```

### 2.2 数据组织特点

1. **项目级别分组**: 按地块和年份组织（如 `corn_001-2013`）
2. **日期级别分组**: 按模拟日期组织（如 `day_05-01`）
3. **数据类型分组**:
   - `input/`: 模型输入对象
   - `output/`: 模型输出对象
   - `state-parameter_variables/`: 状态和参数变量
4. **变量类型区分**:
   - **状态变量**: 随时间变化的系统状态（土壤含水量、LAI等）
   - **参数变量**: 模型参数（土壤导水率、作物系数等）
5. **数据同化支持**: 预留 `assimilated_*` 前缀用于存储同化后的变量

## 3. 数据同化状态管理器

### 3.1 创建独立的状态管理器

**架构决策：** 基于评估结果，创建独立的数据同化状态管理器，确保功能完整性和可靠性。

创建文件 `pyAHC/pyahc/db/data_assimilation_hdf5.py`：

```python
import h5py
import numpy as np
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
import pickle
import logging
import json
import threading
import time
from contextlib import contextmanager
import yaml

from pyahc.model.model import Model
from pyahc.model.result import Result

logger = logging.getLogger(__name__)


class DataAssimilationHDF5:
    """数据同化HDF5管理器 - 为数据同化提供标准化、高性能接口"""

    def __init__(self, db_path: str, config_path: Optional[str] = None,
                 enable_compression: bool = True, chunk_size: int = 1000):
        """
        初始化数据同化HDF5管理器

        Args:
            db_path: HDF5数据库文件路径
            config_path: 状态变量配置文件路径
            enable_compression: 是否启用数据压缩
            chunk_size: 数据分块大小
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.enable_compression = enable_compression
        self.chunk_size = chunk_size
        self._file_lock = threading.RLock()

        # 加载状态变量配置
        self.config = self._load_config(config_path)

        # 初始化数据库结构
        self._initialize_database()

    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载状态变量配置"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    return yaml.safe_load(f)
                else:
                    return json.load(f)

        # 默认配置
        return {
            "state_variables": {
                "soil_moisture": {
                    "csv_columns": ["theta_1", "theta_2", "theta_3", "theta_4", "theta_5"],
                    "alternative_names": ["moisture", "theta", "swc"],
                    "data_type": "array",
                    "required": True,
                    "default_value": [0.25, 0.30, 0.35, 0.40, 0.45]
                },
                "lai": {
                    "csv_columns": ["lai", "LAI", "leaf_area_index"],
                    "alternative_names": ["leaf_area", "canopy_cover"],
                    "data_type": "scalar",
                    "required": True,
                    "default_value": 1.0
                },
                "biomass": {
                    "csv_columns": ["biomass", "bio", "dry_matter"],
                    "alternative_names": ["plant_mass", "vegetation_mass"],
                    "data_type": "scalar",
                    "required": False,
                    "default_value": 1000.0
                },
                "root_depth": {
                    "csv_columns": ["root_depth", "rooting_depth"],
                    "alternative_names": ["root_zone", "effective_root_depth"],
                    "data_type": "scalar",
                    "required": False,
                    "default_value": 50.0
                },
                "groundwater_level": {
                    "csv_columns": ["gwl", "groundwater_level", "water_table"],
                    "alternative_names": ["gw_depth", "phreatic_level"],
                    "data_type": "scalar",
                    "required": False,
                    "default_value": -150.0
                }
            },
            "model_parameters": {
                "hydraulic_conductivity": {
                    "csv_columns": ["ksat", "hydraulic_conductivity"],
                    "data_type": "array",
                    "required": False
                },
                "crop_coefficient": {
                    "csv_columns": ["kc", "crop_coefficient"],
                    "data_type": "scalar",
                    "required": False
                }
            }
        }

    def _initialize_database(self):
        """初始化数据库结构和元数据"""
        with self._safe_file_access("a") as f:
            # 设置全局属性
            f.attrs['created_at'] = datetime.now().isoformat()
            f.attrs['version'] = '1.0.0'
            f.attrs['description'] = 'pyAHC Data Assimilation HDF5 Database'
            f.attrs['config'] = json.dumps(self.config)

    @contextmanager
    def _safe_file_access(self, mode: str = "r"):
        """线程安全的文件访问上下文管理器"""
        with self._file_lock:
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    f = h5py.File(self.db_path, mode)
                    yield f
                    f.close()
                    break
                except (OSError, IOError) as e:
                    if attempt == max_retries - 1:
                        raise e
                    time.sleep(0.1 * (attempt + 1))

    def save_simulation_state(self,
                            project_name: str,
                            simulation_date: date,
                            model: Model,
                            result: Result,
                            state_variables: Optional[Dict] = None,
                            observations: Optional[Dict] = None):
        """
        保存模拟状态（支持数据同化和性能优化）

        Args:
            project_name: 项目名称（如 "corn_field_2013"）
            simulation_date: 模拟日期
            model: 模型对象
            result: 结果对象
            state_variables: 标准化的状态变量字典
            observations: 观测数据字典（可选）
        """
        date_str = simulation_date.strftime("%Y-%m-%d")

        with self._safe_file_access("a") as f:
            # 创建项目组
            project_group = f.require_group(project_name)

            # 创建日期组
            date_group = project_group.require_group(date_str)

            # 保存模型输入（带压缩）
            self._save_pickled_object(date_group, "model_input", model)

            # 保存模型输出（带压缩）
            self._save_pickled_object(date_group, "model_output", result)

            # 保存状态-参数变量（优化存储）
            if state_variables:
                self._save_state_parameter_variables(date_group, state_variables)

            # 保存观测数据
            if observations:
                self._save_observations(date_group, observations)

            # 保存元数据和完整性检查
            self._save_metadata(date_group, simulation_date, model)
            self._save_integrity_check(date_group, state_variables, observations)

        logger.info(f"已保存 {project_name} 项目 {date_str} 的模拟状态")

    def load_simulation_state(self,
                            project_name: str,
                            simulation_date: date) -> Tuple[Model, Result, Dict]:
        """
        加载模拟状态（为数据同化准备）

        Args:
            project_name: 项目名称
            simulation_date: 模拟日期

        Returns:
            (模型对象, 结果对象, 标准化状态变量字典)
        """
        date_str = simulation_date.strftime("%Y-%m-%d")
        
        with h5py.File(self.db_path, "r") as f:
            try:
                date_group = f[project_name][date_str]
                
                # 加载模型和结果
                model = self._load_pickled_object(date_group, "model_input")
                result = self._load_pickled_object(date_group, "model_output")
                
                # 加载状态-参数变量
                state_variables = self._load_state_parameter_variables(date_group)
                
                return model, result, state_variables

            except KeyError:
                raise KeyError(f"未找到项目 {project_name} 日期 {date_str} 的数据")

    def get_state_for_assimilation(self,
                                 project_name: str,
                                 current_date: date) -> Optional[Dict]:
        """
        获取用于数据同化的状态变量（通常是前一天的状态）

        Args:
            project_name: 项目名称
            current_date: 当前日期

        Returns:
            标准化的状态-参数变量字典，供外部数据同化算法使用
        """
        from datetime import timedelta
        previous_date = current_date - timedelta(days=1)
        
        try:
            _, _, state_variables = self.load_simulation_state(project_name, previous_date)
            return state_variables
        except KeyError:
            logger.warning(f"未找到 {previous_date} 的状态-参数变量")
            return None

    def apply_assimilated_state(self,
                              project_name: str,
                              simulation_date: date,
                              assimilated_state: Dict):
        """
        应用数据同化后的状态-参数变量（接口预留）

        Args:
            project_name: 项目名称
            simulation_date: 模拟日期
            assimilated_state: 经过数据同化处理的状态-参数变量
        """
        # 这里预留接口，供外部数据同化算法调用
        # 可以保存同化后的状态，或者直接传递给下一次模拟
        date_str = simulation_date.strftime("%Y-%m-%d")

        with h5py.File(self.db_path, "a") as f:
            project_group = f.require_group(project_name)
            date_group = project_group.require_group(date_str)

            # 保存同化后的状态-参数变量
            self._save_state_parameter_variables(date_group, assimilated_state, prefix="assimilated_")

        logger.info(f"已应用数据同化状态: {project_name} {date_str}")
    
    def list_simulation_dates(self, project_name: str) -> List[date]:
        """
        列出项目中所有的模拟日期
        
        Args:
            project_name: 项目名称
            
        Returns:
            日期列表
        """
        dates = []
        
        with h5py.File(self.db_path, "r") as f:
            if project_name in f:
                for date_str in f[project_name].keys():
                    try:
                        dates.append(datetime.strptime(date_str, "%Y-%m-%d").date())
                    except ValueError:
                        continue
        
        return sorted(dates)
    
    def _save_pickled_object(self, group: h5py.Group, name: str, obj: Any):
        """保存Python对象为pickle格式"""
        try:
            if name in group:
                del group[name]
            
            pickled_data = pickle.dumps(obj)
            group.create_dataset(name, data=np.void(pickled_data))
            
        except Exception as e:
            logger.error(f"保存对象 {name} 失败: {e}")
    
    def _load_pickled_object(self, group: h5py.Group, name: str) -> Any:
        """加载pickle格式的Python对象"""
        try:
            pickled_data = group[name][()].tobytes()
            return pickle.loads(pickled_data)
        except Exception as e:
            logger.error(f"加载对象 {name} 失败: {e}")
            return None
    
    def _save_state_parameter_variables(self, group: h5py.Group, state_vars: Dict, prefix: str = ""):
        """保存状态-参数变量"""
        state_group = group.require_group("state-parameter_variables")

        for var_name, var_value in state_vars.items():
            try:
                full_name = f"{prefix}{var_name}"
                if full_name in state_group:
                    del state_group[full_name]

                if isinstance(var_value, (list, np.ndarray)):
                    state_group.create_dataset(full_name, data=np.array(var_value))
                else:
                    state_group.create_dataset(full_name, data=var_value)

            except Exception as e:
                logger.error(f"保存状态-参数变量 {var_name} 失败: {e}")

    def _load_state_parameter_variables(self, group: h5py.Group) -> Dict:
        """加载状态-参数变量"""
        state_vars = {}

        if "state-parameter_variables" in group:
            state_group = group["state-parameter_variables"]
            for var_name in state_group.keys():
                try:
                    data = state_group[var_name][()]
                    if isinstance(data, np.ndarray) and data.ndim == 0:
                        state_vars[var_name] = data.item()
                    else:
                        state_vars[var_name] = data
                except Exception as e:
                    logger.error(f"加载状态-参数变量 {var_name} 失败: {e}")
        
        return state_vars
    
    def _save_metadata(self, group: h5py.Group, simulation_date: date, model: Model):
        """保存元数据"""
        metadata = {
            "simulation_date": simulation_date.isoformat(),
            "created_at": datetime.now().isoformat(),
            "model_version": getattr(model, 'version', 'unknown'),
            "project_name": getattr(model.metadata, 'project_name', 'unknown') if hasattr(model, 'metadata') else 'unknown'
        }
        
        for key, value in metadata.items():
            if isinstance(value, str):
                group.attrs[key] = value
```

## 4. 标准化状态-参数变量接口

### 4.1 创建配置驱动的状态变量提取器

**架构改进：** 使用配置文件驱动的变量映射，提高健壮性和可维护性。

创建文件 `pyAHC/pyahc/utils/state_extractor.py`：

```python
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
import logging
import json
import yaml
from pathlib import Path

from pyahc.model.result import Result

logger = logging.getLogger(__name__)


class ConfigurableStateExtractor:
    """配置驱动的状态变量提取器 - 提供健壮的数据提取接口"""

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化状态变量提取器

        Args:
            config: 状态变量配置字典，如果为None则使用默认配置
        """
        self.config = config or self._get_default_config()
        self.extraction_stats = {
            'successful_extractions': 0,
            'failed_extractions': 0,
            'missing_variables': [],
            'extraction_errors': []
        }

    def extract_all_state_variables(self, result: Result,
                                  validate_required: bool = True) -> Dict:
        """
        提取所有关键状态-参数变量（配置驱动的健壮提取）

        Args:
            result: 模型结果对象
            validate_required: 是否验证必需变量

        Returns:
            标准化的状态-参数变量字典，键名统一，便于数据同化算法使用
        """
        state_vars = {}
        self.extraction_stats = {
            'successful_extractions': 0,
            'failed_extractions': 0,
            'missing_variables': [],
            'extraction_errors': []
        }

        # 提取状态变量
        for var_name, var_config in self.config.get('state_variables', {}).items():
            try:
                extracted_value = self._extract_variable(result, var_name, var_config)
                if extracted_value is not None:
                    state_vars[var_name] = extracted_value
                    self.extraction_stats['successful_extractions'] += 1
                elif var_config.get('required', False):
                    if validate_required:
                        # 使用默认值
                        default_value = var_config.get('default_value')
                        if default_value is not None:
                            state_vars[var_name] = default_value
                            logger.warning(f"使用默认值替代缺失的必需变量 {var_name}: {default_value}")
                        else:
                            self.extraction_stats['missing_variables'].append(var_name)
                            logger.error(f"缺失必需的状态变量: {var_name}")
                    else:
                        self.extraction_stats['missing_variables'].append(var_name)
                else:
                    self.extraction_stats['failed_extractions'] += 1

            except Exception as e:
                self.extraction_stats['extraction_errors'].append(f"{var_name}: {str(e)}")
                logger.error(f"提取状态变量 {var_name} 时出错: {e}")

        # 提取模型参数
        for param_name, param_config in self.config.get('model_parameters', {}).items():
            try:
                extracted_value = self._extract_variable(result, param_name, param_config)
                if extracted_value is not None:
                    state_vars[f"param_{param_name}"] = extracted_value
                    self.extraction_stats['successful_extractions'] += 1
            except Exception as e:
                self.extraction_stats['extraction_errors'].append(f"param_{param_name}: {str(e)}")
                logger.error(f"提取模型参数 {param_name} 时出错: {e}")

        # 记录提取统计
        logger.info(f"状态变量提取完成: 成功 {self.extraction_stats['successful_extractions']}, "
                   f"失败 {self.extraction_stats['failed_extractions']}, "
                   f"缺失 {len(self.extraction_stats['missing_variables'])}")

        return state_vars

    def _get_default_config(self) -> Dict:
        """获取默认的状态变量配置"""
        return {
            "state_variables": {
                "soil_moisture": {
                    "csv_columns": ["theta_1", "theta_2", "theta_3", "theta_4", "theta_5"],
                    "alternative_names": ["moisture", "theta", "swc", "soil_water_content"],
                    "data_type": "array",
                    "required": True,
                    "default_value": [0.25, 0.30, 0.35, 0.40, 0.45],
                    "validation": {"min": 0.0, "max": 1.0}
                },
                "lai": {
                    "csv_columns": ["lai", "LAI", "leaf_area_index"],
                    "alternative_names": ["leaf_area", "canopy_cover"],
                    "data_type": "scalar",
                    "required": True,
                    "default_value": 1.0,
                    "validation": {"min": 0.0, "max": 10.0}
                },
                "biomass": {
                    "csv_columns": ["biomass", "bio", "dry_matter", "plant_mass"],
                    "alternative_names": ["vegetation_mass", "above_ground_biomass"],
                    "data_type": "scalar",
                    "required": False,
                    "default_value": 1000.0,
                    "validation": {"min": 0.0}
                },
                "root_depth": {
                    "csv_columns": ["root_depth", "rooting_depth", "effective_root_depth"],
                    "alternative_names": ["root_zone", "root_zone_depth"],
                    "data_type": "scalar",
                    "required": False,
                    "default_value": 50.0,
                    "validation": {"min": 0.0, "max": 300.0}
                },
                "groundwater_level": {
                    "csv_columns": ["gwl", "groundwater_level", "water_table", "gw_depth"],
                    "alternative_names": ["phreatic_level", "water_table_depth"],
                    "data_type": "scalar",
                    "required": False,
                    "default_value": -150.0,
                    "validation": {"max": 0.0}
                }
            },
            "model_parameters": {
                "hydraulic_conductivity": {
                    "csv_columns": ["ksat", "hydraulic_conductivity", "saturated_conductivity"],
                    "data_type": "array",
                    "required": False
                },
                "crop_coefficient": {
                    "csv_columns": ["kc", "crop_coefficient", "crop_factor"],
                    "data_type": "scalar",
                    "required": False
                }
            }
        }

    def _extract_variable(self, result: Result, var_name: str, var_config: Dict) -> Optional[Any]:
        """
        配置驱动的变量提取方法

        Args:
            result: 模型结果对象
            var_name: 变量名称
            var_config: 变量配置

        Returns:
            提取的变量值或None
        """
        # 首先尝试从CSV输出中提取
        extracted_value = self._extract_from_csv(result, var_config)

        # 如果CSV中没有，尝试从其他输出格式中提取
        if extracted_value is None:
            extracted_value = self._extract_from_output_dict(result, var_config)

        # 验证提取的值
        if extracted_value is not None:
            extracted_value = self._validate_and_convert(extracted_value, var_config)

        return extracted_value

    def _extract_from_csv(self, result: Result, var_config: Dict) -> Optional[Any]:
        """从CSV输出中提取变量"""
        if result.csv is None or result.csv.empty:
            return None

        df = result.csv

        # 尝试配置中指定的列名
        for col_name in var_config.get('csv_columns', []):
            if col_name in df.columns:
                return self._get_last_value(df[col_name], var_config['data_type'])

        # 尝试替代名称（模糊匹配）
        for alt_name in var_config.get('alternative_names', []):
            matching_cols = [col for col in df.columns if alt_name.lower() in col.lower()]
            if matching_cols:
                return self._get_last_value(df[matching_cols[0]], var_config['data_type'])

        return None

    def _extract_from_output_dict(self, result: Result, var_config: Dict) -> Optional[Any]:
        """从输出字典中提取变量"""
        if not result.output:
            return None

        # 搜索所有可能的键名
        search_names = var_config.get('csv_columns', []) + var_config.get('alternative_names', [])

        for key, data in result.output.items():
            for search_name in search_names:
                if search_name.lower() in key.lower():
                    if isinstance(data, pd.DataFrame):
                        return self._get_last_value(data.iloc[:, 0], var_config['data_type'])
                    elif isinstance(data, (int, float)):
                        return float(data)
                    elif isinstance(data, (list, np.ndarray)) and len(data) > 0:
                        if var_config['data_type'] == 'array':
                            return list(data)
                        else:
                            return float(data[-1])

        return None

    def _get_last_value(self, series: pd.Series, data_type: str) -> Optional[Any]:
        """获取序列的最后一个值"""
        if series.empty:
            return None

        if data_type == 'array':
            return series.tolist()
        else:
            return float(series.iloc[-1])

    def _validate_and_convert(self, value: Any, var_config: Dict) -> Optional[Any]:
        """验证和转换提取的值"""
        try:
            validation = var_config.get('validation', {})

            if var_config['data_type'] == 'array':
                if not isinstance(value, list):
                    value = [float(value)]

                # 验证数组中的每个值
                validated_array = []
                for v in value:
                    v = float(v)
                    if 'min' in validation and v < validation['min']:
                        logger.warning(f"值 {v} 小于最小值 {validation['min']}")
                        v = validation['min']
                    if 'max' in validation and v > validation['max']:
                        logger.warning(f"值 {v} 大于最大值 {validation['max']}")
                        v = validation['max']
                    validated_array.append(v)
                return validated_array

            else:  # scalar
                value = float(value)
                if 'min' in validation and value < validation['min']:
                    logger.warning(f"值 {value} 小于最小值 {validation['min']}")
                    value = validation['min']
                if 'max' in validation and value > validation['max']:
                    logger.warning(f"值 {value} 大于最大值 {validation['max']}")
                    value = validation['max']
                return value

        except (ValueError, TypeError) as e:
            logger.error(f"验证和转换值时出错: {e}")
            return None

    def get_extraction_stats(self) -> Dict:
        """获取提取统计信息"""
        return self.extraction_stats.copy()

    @staticmethod
    def extract_soil_moisture(result: Result) -> Optional[List[float]]:
        """提取土壤含水量"""
        try:
            if result.csv is not None:
                # 从CSV输出中提取最后一天的土壤含水量
                df = result.csv
                if not df.empty:
                    # 查找土壤含水量相关列
                    moisture_cols = [col for col in df.columns if 'theta' in col.lower() or 'moisture' in col.lower()]
                    if moisture_cols:
                        last_row = df.iloc[-1]
                        return [last_row[col] for col in moisture_cols]
            
            # 如果CSV中没有，尝试从其他输出格式中提取
            if result.output:
                for key, data in result.output.items():
                    if 'moisture' in key.lower() or 'theta' in key.lower():
                        # 处理不同格式的数据
                        if isinstance(data, pd.DataFrame):
                            return data.iloc[-1].tolist()
                        elif isinstance(data, (list, np.ndarray)):
                            return list(data)
            
        except Exception as e:
            logger.error(f"提取土壤含水量失败: {e}")
        
        return None
    
    @staticmethod
    def extract_lai(result: Result) -> Optional[float]:
        """提取叶面积指数"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    lai_cols = [col for col in df.columns if 'lai' in col.lower()]
                    if lai_cols:
                        return float(df.iloc[-1][lai_cols[0]])
            
            # 从其他输出格式中查找
            if result.output:
                for key, data in result.output.items():
                    if 'lai' in key.lower():
                        if isinstance(data, pd.DataFrame):
                            return float(data.iloc[-1].iloc[0])
                        elif isinstance(data, (int, float)):
                            return float(data)
                        elif isinstance(data, (list, np.ndarray)) and len(data) > 0:
                            return float(data[-1])
            
        except Exception as e:
            logger.error(f"提取LAI失败: {e}")
        
        return None
    
    @staticmethod
    def extract_biomass(result: Result) -> Optional[float]:
        """提取生物量"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    biomass_cols = [col for col in df.columns if 'biomass' in col.lower() or 'bio' in col.lower()]
                    if biomass_cols:
                        return float(df.iloc[-1][biomass_cols[0]])
            
        except Exception as e:
            logger.error(f"提取生物量失败: {e}")
        
        return None
    
    @staticmethod
    def extract_root_depth(result: Result) -> Optional[float]:
        """提取根深"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    root_cols = [col for col in df.columns if 'root' in col.lower() and 'depth' in col.lower()]
                    if root_cols:
                        return float(df.iloc[-1][root_cols[0]])
            
        except Exception as e:
            logger.error(f"提取根深失败: {e}")
        
        return None
    
    @staticmethod
    def extract_groundwater_level(result: Result) -> Optional[float]:
        """提取地下水位"""
        try:
            if result.csv is not None:
                df = result.csv
                if not df.empty:
                    gw_cols = [col for col in df.columns if 'gwl' in col.lower() or 'groundwater' in col.lower()]
                    if gw_cols:
                        return float(df.iloc[-1][gw_cols[0]])
            
        except Exception as e:
            logger.error(f"提取地下水位失败: {e}")

        return None

    @staticmethod
    def extract_model_parameters(result: Result) -> Optional[Dict]:
        """提取模型参数（参数变量）"""
        try:
            parameters = {}

            # 提取土壤水力参数
            if result.output:
                for key, data in result.output.items():
                    if 'param' in key.lower() or 'parameter' in key.lower():
                        if isinstance(data, pd.DataFrame):
                            # 从DataFrame中提取参数
                            param_cols = [col for col in data.columns if 'param' in col.lower()]
                            for col in param_cols:
                                parameters[f"param_{col}"] = float(data.iloc[-1][col])
                        elif isinstance(data, (int, float)):
                            parameters[key] = float(data)

            # 可以添加更多参数提取逻辑
            # 例如：土壤导水率、作物参数等

            return parameters if parameters else None

        except Exception as e:
            logger.error(f"提取模型参数失败: {e}")

        return None
```

### 4.2 创建增强的数据同化接口

创建文件 `pyAHC/pyahc/utils/data_assimilation_interface.py`：

```python
from typing import Dict, Optional, List, Tuple, Any, Union
import logging
import numpy as np
import pandas as pd
from datetime import date, datetime
from pathlib import Path
import json

from pyahc.model.model import Model

logger = logging.getLogger(__name__)


class DataAssimilationInterface:
    """数据同化接口 - 提供标准化的数据同化算法集成接口"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化数据同化接口

        Args:
            config_path: 数据同化配置文件路径
        """
        self.config = self._load_config(config_path)
        self.observation_manager = ObservationManager()
        self.covariance_manager = CovarianceManager()

    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载数据同化配置"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        # 默认配置
        return {
            "assimilation_methods": ["enkf", "3dvar", "4dvar"],
            "state_vector_config": {
                "variables": ["soil_moisture", "lai", "biomass"],
                "error_correlation": True,
                "localization_radius": 50.0
            },
            "observation_config": {
                "supported_formats": ["csv", "netcdf", "json"],
                "quality_control": True,
                "error_estimation": "adaptive"
            }
        }

    def create_assimilation_package(self,
                                  state_variables: Dict,
                                  observations: Optional[Dict] = None,
                                  background_error_cov: Optional[np.ndarray] = None) -> Dict:
        """
        创建标准化的数据同化包

        Args:
            state_variables: 模型状态变量字典
            observations: 观测数据字典
            background_error_cov: 背景误差协方差矩阵

        Returns:
            标准化的数据同化包
        """
        # 创建状态向量
        state_vector, variable_mapping = self._create_state_vector(state_variables)

        # 处理观测数据
        observation_vector, observation_operator = self._process_observations(
            observations, variable_mapping
        )

        # 创建或估计误差协方差矩阵
        if background_error_cov is None:
            background_error_cov = self.covariance_manager.estimate_background_error_covariance(
                state_variables
            )

        observation_error_cov = self.covariance_manager.estimate_observation_error_covariance(
            observations
        )

        # 构建数据同化包
        assimilation_package = {
            "state_vector": state_vector,
            "variable_mapping": variable_mapping,
            "observations": {
                "vector": observation_vector,
                "operator": observation_operator,
                "error_covariance": observation_error_cov
            },
            "background": {
                "error_covariance": background_error_cov,
                "ensemble_size": self.config.get("ensemble_size", 50)
            },
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "variables": list(state_variables.keys()),
                "observation_count": len(observation_vector) if observation_vector is not None else 0
            }
        }

        return assimilation_package

    def _create_state_vector(self, state_variables: Dict) -> Tuple[np.ndarray, Dict]:
        """创建状态向量和变量映射"""
        state_vector = []
        variable_mapping = {}
        current_index = 0

        for var_name, var_value in state_variables.items():
            if isinstance(var_value, list):
                var_length = len(var_value)
                state_vector.extend(var_value)
            else:
                var_length = 1
                state_vector.append(var_value)

            variable_mapping[var_name] = {
                "start_index": current_index,
                "length": var_length,
                "data_type": "array" if isinstance(var_value, list) else "scalar"
            }
            current_index += var_length

        return np.array(state_vector), variable_mapping

    def _process_observations(self, observations: Optional[Dict],
                            variable_mapping: Dict) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """处理观测数据"""
        if not observations:
            return None, None

        observation_vector = []
        observation_operator_rows = []

        for obs_var, obs_data in observations.items():
            if obs_var in variable_mapping:
                var_info = variable_mapping[obs_var]

                if isinstance(obs_data, dict) and 'values' in obs_data:
                    obs_values = obs_data['values']
                    obs_errors = obs_data.get('errors', None)
                else:
                    obs_values = obs_data
                    obs_errors = None

                # 创建观测算子行
                operator_row = np.zeros(len(sum(variable_mapping.values(), [])))
                start_idx = var_info["start_index"]
                end_idx = start_idx + var_info["length"]

                if isinstance(obs_values, list):
                    observation_vector.extend(obs_values)
                    operator_row[start_idx:end_idx] = 1.0
                else:
                    observation_vector.append(obs_values)
                    operator_row[start_idx] = 1.0

                observation_operator_rows.append(operator_row)

        if observation_vector:
            return np.array(observation_vector), np.array(observation_operator_rows)
        else:
            return None, None

    def apply_assimilated_state(self, model: Model,
                              assimilated_state: Dict,
                              original_state: Dict) -> Model:
        """
        将数据同化后的状态应用到模型

        Args:
            model: 模型对象
            assimilated_state: 同化后的状态变量
            original_state: 原始状态变量

        Returns:
            更新后的模型对象
        """
        try:
            # 更新土壤含水量
            if 'soil_moisture' in assimilated_state and hasattr(model, 'soilmoisture'):
                if hasattr(model.soilmoisture, 'thetai'):
                    model.soilmoisture.thetai = assimilated_state['soil_moisture']
                    logger.info(f"✓ 更新土壤含水量: {len(assimilated_state['soil_moisture'])} 层")

            # 更新作物LAI（根据模型结构调整）
            if 'lai' in assimilated_state:
                # 这里需要根据具体的模型结构来设置LAI
                logger.info(f"✓ 准备更新LAI: {assimilated_state['lai']}")

            # 更新生物量
            if 'biomass' in assimilated_state:
                logger.info(f"✓ 准备更新生物量: {assimilated_state['biomass']}")

            # 更新根深
            if 'root_depth' in assimilated_state:
                logger.info(f"✓ 准备更新根深: {assimilated_state['root_depth']}")

            # 更新地下水位
            if 'groundwater_level' in assimilated_state:
                logger.info(f"✓ 准备更新地下水位: {assimilated_state['groundwater_level']}")

            # 记录同化效果
            self._log_assimilation_impact(original_state, assimilated_state)

        except Exception as e:
            logger.error(f"应用同化状态时出错: {e}")

        return model

    def _log_assimilation_impact(self, original_state: Dict, assimilated_state: Dict):
        """记录数据同化的影响"""
        for var_name in original_state.keys():
            if var_name in assimilated_state:
                orig_val = original_state[var_name]
                assim_val = assimilated_state[var_name]

                if isinstance(orig_val, list) and isinstance(assim_val, list):
                    diff = np.array(assim_val) - np.array(orig_val)
                    logger.info(f"变量 {var_name} 同化调整: 平均变化 {np.mean(diff):.4f}")
                else:
                    diff = float(assim_val) - float(orig_val)
                    logger.info(f"变量 {var_name} 同化调整: {orig_val:.4f} -> {assim_val:.4f} (变化: {diff:.4f})")


class ObservationManager:
    """观测数据管理器"""

    def __init__(self):
        self.supported_formats = ['csv', 'json', 'netcdf']
        self.quality_control_enabled = True

    def load_observations(self, file_path: str, format_type: str = 'auto') -> Dict:
        """
        加载观测数据

        Args:
            file_path: 观测数据文件路径
            format_type: 数据格式类型

        Returns:
            标准化的观测数据字典
        """
        if format_type == 'auto':
            format_type = self._detect_format(file_path)

        if format_type == 'csv':
            return self._load_csv_observations(file_path)
        elif format_type == 'json':
            return self._load_json_observations(file_path)
        elif format_type == 'netcdf':
            return self._load_netcdf_observations(file_path)
        else:
            raise ValueError(f"不支持的观测数据格式: {format_type}")

    def _detect_format(self, file_path: str) -> str:
        """自动检测文件格式"""
        suffix = Path(file_path).suffix.lower()
        if suffix in ['.csv']:
            return 'csv'
        elif suffix in ['.json']:
            return 'json'
        elif suffix in ['.nc', '.netcdf']:
            return 'netcdf'
        else:
            raise ValueError(f"无法识别的文件格式: {suffix}")

    def _load_csv_observations(self, file_path: str) -> Dict:
        """加载CSV格式的观测数据"""
        df = pd.read_csv(file_path)
        observations = {}

        for column in df.columns:
            if column.lower() in ['date', 'time', 'datetime']:
                continue

            values = df[column].dropna().tolist()
            if values:
                observations[column] = {
                    'values': values,
                    'timestamps': df['date'].tolist() if 'date' in df.columns else None,
                    'errors': None  # 可以从额外的列中读取
                }

        return observations

    def _load_json_observations(self, file_path: str) -> Dict:
        """加载JSON格式的观测数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _load_netcdf_observations(self, file_path: str) -> Dict:
        """加载NetCDF格式的观测数据"""
        try:
            import xarray as xr
            ds = xr.open_dataset(file_path)
            observations = {}

            for var_name in ds.data_vars:
                observations[var_name] = {
                    'values': ds[var_name].values.tolist(),
                    'coordinates': {coord: ds[coord].values.tolist() for coord in ds[var_name].coords},
                    'attributes': dict(ds[var_name].attrs)
                }

            return observations
        except ImportError:
            raise ImportError("需要安装 xarray 库来支持 NetCDF 格式")


class CovarianceManager:
    """协方差矩阵管理器"""

    def __init__(self):
        self.default_error_ratios = {
            'soil_moisture': 0.1,  # 10% 相对误差
            'lai': 0.15,           # 15% 相对误差
            'biomass': 0.2,        # 20% 相对误差
            'root_depth': 0.1,     # 10% 相对误差
            'groundwater_level': 0.05  # 5% 相对误差
        }

    def estimate_background_error_covariance(self, state_variables: Dict) -> np.ndarray:
        """
        估计背景误差协方差矩阵

        Args:
            state_variables: 状态变量字典

        Returns:
            背景误差协方差矩阵
        """
        # 创建状态向量
        state_vector = []
        for var_name, var_value in state_variables.items():
            if isinstance(var_value, list):
                state_vector.extend(var_value)
            else:
                state_vector.append(var_value)

        n_states = len(state_vector)
        cov_matrix = np.zeros((n_states, n_states))

        # 填充对角线元素（方差）
        idx = 0
        for var_name, var_value in state_variables.items():
            error_ratio = self.default_error_ratios.get(var_name, 0.1)

            if isinstance(var_value, list):
                for val in var_value:
                    variance = (val * error_ratio) ** 2
                    cov_matrix[idx, idx] = variance
                    idx += 1
            else:
                variance = (var_value * error_ratio) ** 2
                cov_matrix[idx, idx] = variance
                idx += 1

        # 添加相关性（简化处理）
        self._add_spatial_correlation(cov_matrix, state_variables)

        return cov_matrix

    def estimate_observation_error_covariance(self, observations: Optional[Dict]) -> Optional[np.ndarray]:
        """
        估计观测误差协方差矩阵

        Args:
            observations: 观测数据字典

        Returns:
            观测误差协方差矩阵
        """
        if not observations:
            return None

        # 计算观测向量长度
        obs_length = 0
        for obs_var, obs_data in observations.items():
            if isinstance(obs_data, dict) and 'values' in obs_data:
                obs_values = obs_data['values']
            else:
                obs_values = obs_data

            if isinstance(obs_values, list):
                obs_length += len(obs_values)
            else:
                obs_length += 1

        # 创建对角协方差矩阵
        cov_matrix = np.zeros((obs_length, obs_length))
        idx = 0

        for obs_var, obs_data in observations.items():
            if isinstance(obs_data, dict):
                obs_values = obs_data.get('values', obs_data)
                obs_errors = obs_data.get('errors', None)
            else:
                obs_values = obs_data
                obs_errors = None

            if isinstance(obs_values, list):
                for i, val in enumerate(obs_values):
                    if obs_errors and len(obs_errors) > i:
                        variance = obs_errors[i] ** 2
                    else:
                        # 使用默认误差比例
                        error_ratio = self.default_error_ratios.get(obs_var, 0.1)
                        variance = (val * error_ratio) ** 2
                    cov_matrix[idx, idx] = variance
                    idx += 1
            else:
                if obs_errors:
                    variance = obs_errors ** 2
                else:
                    error_ratio = self.default_error_ratios.get(obs_var, 0.1)
                    variance = (obs_values * error_ratio) ** 2
                cov_matrix[idx, idx] = variance
                idx += 1

        return cov_matrix

    def _add_spatial_correlation(self, cov_matrix: np.ndarray, state_variables: Dict):
        """添加空间相关性（简化实现）"""
        # 这里可以实现更复杂的空间相关性模型
        # 目前使用简单的指数衰减模型
        correlation_length = 2  # 相关长度

        for i in range(cov_matrix.shape[0]):
            for j in range(i+1, cov_matrix.shape[1]):
                distance = abs(i - j)
                correlation = np.exp(-distance / correlation_length)
                covariance = correlation * np.sqrt(cov_matrix[i, i] * cov_matrix[j, j])
                cov_matrix[i, j] = covariance
                cov_matrix[j, i] = covariance

    @staticmethod
    def create_assimilation_interface(state_variables: Dict) -> Dict:
        """
        创建数据同化接口格式

        Args:
            state_variables: 模型状态变量

        Returns:
            适合外部数据同化算法的标准格式
        """
        # 转换为数据同化算法期望的格式
        assimilation_format = {
            'state_vector': [],
            'variable_names': [],
            'variable_types': [],
            'dimensions': []
        }

        for var_name, var_value in state_variables.items():
            assimilation_format['variable_names'].append(var_name)

            if isinstance(var_value, list):
                assimilation_format['state_vector'].extend(var_value)
                assimilation_format['variable_types'].append('array')
                assimilation_format['dimensions'].append(len(var_value))
            else:
                assimilation_format['state_vector'].append(var_value)
                assimilation_format['variable_types'].append('scalar')
                assimilation_format['dimensions'].append(1)

        return assimilation_format

    @staticmethod
    def parse_assimilated_result(assimilated_data: Dict,
                               original_format: Dict) -> Dict:
        """
        解析数据同化结果，转换回模型格式

        Args:
            assimilated_data: 数据同化算法的输出
            original_format: 原始状态变量格式

        Returns:
            标准化的状态变量字典
        """
        # 这里提供接口，将外部数据同化算法的结果转换回模型可用的格式
        # 具体实现取决于使用的数据同化算法

        result_state = {}

        # 示例：简单的格式转换
        if 'state_vector' in assimilated_data:
            state_vector = assimilated_data['state_vector']
            var_names = original_format.get('variable_names', [])
            dimensions = original_format.get('dimensions', [])

            idx = 0
            for i, var_name in enumerate(var_names):
                dim = dimensions[i]
                if dim == 1:
                    result_state[var_name] = state_vector[idx]
                    idx += 1
                else:
                    result_state[var_name] = state_vector[idx:idx+dim]
                    idx += dim

        return result_state
```

## 5. 模拟工作流管理器

创建文件 `pyAHC/pyahc/utils/simulation_workflow.py`：

```python
from datetime import date, timedelta
from typing import Dict, Optional, Callable, List
import logging

from pyahc.model.model import Model
from pyahc.db.state_manager import StateVariableManager
from pyahc.utils.state_extractor import StateVariableExtractor
from pyahc.utils.state_setter import StateVariableSetter

logger = logging.getLogger(__name__)


class SimulationWorkflow:
    """模拟工作流管理器 - 支持数据同化的连续模拟"""
    
    def __init__(self,
                 db_path: str,
                 project_name: str,
                 model_creator: Callable[[date, date, Optional[Dict]], Model]):
        """
        初始化模拟工作流

        Args:
            db_path: HDF5数据库路径
            project_name: 项目名称
            model_creator: 模型创建函数，接受(start_date, end_date, initial_state)参数
        """
        self.state_manager = StateVariableManager(db_path)
        self.project_name = project_name
        self.model_creator = model_creator
        self.extractor = StateVariableExtractor()
        self.setter = StateVariableSetter()
    
    def run_daily_simulation(self,
                           simulation_date: date,
                           external_assimilation_func: Optional[Callable] = None) -> Dict:
        """
        运行单日模拟（支持外部数据同化）

        Args:
            simulation_date: 模拟日期
            external_assimilation_func: 外部数据同化函数（可选）
                                      函数签名: func(state_dict) -> assimilated_state_dict

        Returns:
            标准化状态变量字典
        """
        # 获取前一天的状态变量
        previous_state = self.state_manager.get_state_for_assimilation(
            self.project_name, simulation_date
        )

        # 如果有外部数据同化函数，应用数据同化
        if previous_state and external_assimilation_func:
            try:
                # 转换为数据同化接口格式
                assimilation_format = self.setter.create_assimilation_interface(previous_state)

                # 调用外部数据同化算法
                assimilated_format = external_assimilation_func(assimilation_format)

                # 转换回模型格式
                previous_state = self.setter.parse_assimilated_result(
                    assimilated_format, assimilation_format
                )

                # 保存同化后的状态
                self.state_manager.apply_assimilated_state(
                    self.project_name, simulation_date, previous_state
                )

                logger.info(f"对 {simulation_date} 应用了外部数据同化")
            except Exception as e:
                logger.error(f"数据同化失败: {e}")

        # 创建模型
        end_date = simulation_date + timedelta(days=1)
        model = self.model_creator(simulation_date, end_date, previous_state)

        # 应用状态变量到模型
        if previous_state:
            model = self.setter.apply_state_to_model(model, previous_state)

        # 运行模型
        result = model.run()

        # 提取状态变量
        state_variables = self.extractor.extract_all_state_variables(result)

        # 保存结果
        self.state_manager.save_simulation_state(
            self.project_name, simulation_date, model, result, state_variables
        )

        logger.info(f"完成 {simulation_date} 的模拟")
        return state_variables
    
    def run_period_simulation(self,
                            start_date: date,
                            end_date: date,
                            external_assimilation_func: Optional[Callable] = None):
        """
        运行时间段连续模拟

        Args:
            start_date: 开始日期
            end_date: 结束日期
            external_assimilation_func: 外部数据同化函数（可选）
        """
        current_date = start_date

        while current_date <= end_date:
            try:
                self.run_daily_simulation(
                    current_date, external_assimilation_func
                )
            except Exception as e:
                logger.error(f"模拟 {current_date} 时出错: {e}")

            current_date += timedelta(days=1)
    
    def get_simulation_summary(self) -> Dict:
        """获取模拟摘要"""
        dates = self.state_manager.list_simulation_dates(self.project_name)
        
        if not dates:
            return {"message": "没有找到模拟数据"}
        
        return {
            "project_name": self.project_name,
            "simulation_dates": dates,
            "start_date": min(dates),
            "end_date": max(dates),
            "total_days": len(dates)
        }

    def get_assimilation_interface(self, simulation_date: date) -> Optional[Dict]:
        """
        获取数据同化接口（供外部算法调用）

        Args:
            simulation_date: 模拟日期

        Returns:
            标准化的数据同化接口格式
        """
        state_vars = self.state_manager.get_state_for_assimilation(
            self.project_name, simulation_date
        )

        if state_vars:
            return self.setter.create_assimilation_interface(state_vars)
        return None
```

## 6. 项目升级使用示例

### 6.1 基础状态-参数管理示例

创建文件 `pyAHC/examples/state_management_example.py`：

```python
#!/usr/bin/env python3
"""HDF5数据同化使用示例"""

import sys
from pathlib import Path
from datetime import date, datetime, timedelta
import numpy as np

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from pyahc.db.state_manager import StateVariableManager
from pyahc.utils.state_extractor import StateVariableExtractor
from pyahc.utils.state_setter import StateVariableSetter
from pyahc.utils.simulation_workflow import SimulationWorkflow
from pyahc.model.model import Model, ModelBuilder
from hetao_corn_2013 import create_model_components


def create_model_with_initial_state(start_date: date,
                                   end_date: date,
                                   initial_state: dict = None) -> Model:
    """
    创建带有初始状态的模型

    Args:
        start_date: 开始日期
        end_date: 结束日期
        initial_state: 初始状态变量字典

    Returns:
        配置好的模型对象
    """
    # 使用现有的模型创建函数
    model, _, _, _, _ = create_model_components()

    # 更新模拟时间
    model.generalsettings.tstart = start_date
    model.generalsettings.tend = end_date

    # 如果有初始状态，更新模型参数
    if initial_state:
        update_model_initial_conditions(model, initial_state)

    return model


def external_assimilation_example(assimilation_interface: Dict) -> Dict:
    """
    外部数据同化算法示例接口

    Args:
        assimilation_interface: 标准化的数据同化接口

    Returns:
        同化后的数据（相同格式）
    """
    # 这里是外部数据同化算法的接口示例
    # 实际使用时，这里会调用具体的数据同化算法（如EnKF、3DVar等）

    print("🔄 调用外部数据同化算法...")
    print(f"  输入变量: {assimilation_interface['variable_names']}")
    print(f"  状态向量长度: {len(assimilation_interface['state_vector'])}")

    # 模拟数据同化过程（实际中会被真实的算法替换）
    assimilated_interface = assimilation_interface.copy()

    # 示例：对状态向量进行简单的调整
    state_vector = assimilated_interface['state_vector'].copy()
    for i in range(len(state_vector)):
        # 添加小的随机扰动模拟同化效果
        state_vector[i] = state_vector[i] * (1 + np.random.normal(0, 0.01))

    assimilated_interface['state_vector'] = state_vector

    print("✓ 数据同化完成")
    return assimilated_interface


def example_1_basic_usage():
    """示例1: 基础HDF5使用"""
    print("=== 示例1: 基础HDF5使用 ===")

    # 初始化HDF5状态管理器
    state_manager = StateVariableManager("data/corn_simulation.h5")

    # 创建模型并运行
    start_date = date(2013, 5, 1)
    end_date = date(2013, 5, 2)

    model = create_model_with_initial_state(start_date, end_date)

    # 运行模型（这里使用模拟的结果）
    print("运行模型...")
    # result = model.run()  # 实际运行

    # 模拟结果对象（用于演示）
    from pyahc.model.result import Result
    import pandas as pd

    # 创建模拟的CSV数据
    mock_csv = pd.DataFrame({
        'DATETIME': [datetime(2013, 5, 2)],
        'theta_1': [0.25],
        'theta_2': [0.30],
        'theta_3': [0.35],
        'lai': [2.5],
        'biomass': [1500.0],
        'gwl': [-150.0]
    })
    mock_csv.set_index('DATETIME', inplace=True)

    result = Result(
        log="模拟运行日志",
        output={'csv': mock_csv},
        warning=[]
    )

    # 提取状态变量
    extractor = StateVariableExtractor()
    state_vars = extractor.extract_all_state_variables(result)
    print(f"提取的状态变量: {list(state_vars.keys())}")

    # 保存到HDF5
    state_manager.save_simulation_state(
        project_name="corn_001-2013",
        simulation_date=start_date,
        model=model,
        result=result,
        state_variables=state_vars
    )

    print("✓ 数据已保存到HDF5")

    # 加载数据
    loaded_model, loaded_result, loaded_state = state_manager.load_simulation_state(
        "corn_001-2013", start_date
    )

    print(f"✓ 数据已从HDF5加载")
    print(f"  加载的状态变量: {list(loaded_state.keys())}")


def example_2_workflow_usage():
    """示例2: 数据同化工作流使用"""
    print("\n=== 示例2: 数据同化工作流使用 ===")

    # 初始化工作流
    workflow = DataAssimilationWorkflow(
        db_path="data/corn_workflow.h5",
        project_name="corn_field_2013",
        model_creator=create_model_with_initial_state
    )

    # 准备观测数据
    observations_data = {
        date(2013, 5, 2): {
            'soil_moisture_obs': [0.26, 0.31, 0.36],
            'lai_obs': 2.3
        },
        date(2013, 5, 4): {
            'soil_moisture_obs': [0.24, 0.29, 0.34],
            'lai_obs': 2.8
        }
    }

    # 运行时间段模拟
    start_date = date(2013, 5, 1)
    end_date = date(2013, 5, 5)

    print(f"运行 {start_date} 到 {end_date} 的模拟...")

    workflow.run_period_simulation(
        start_date=start_date,
        end_date=end_date,
        observations_dict=observations_data,
        assimilation_func=simple_data_assimilation
    )

    # 获取模拟摘要
    summary = workflow.get_simulation_summary()
    print(f"✓ 模拟完成")
    print(f"  项目: {summary['project_name']}")
    print(f"  模拟天数: {summary['total_days']}")
    print(f"  日期范围: {summary['start_date']} 到 {summary['end_date']}")


def example_3_manual_daily_simulation():
    """示例3: 手动逐日模拟"""
    print("\n=== 示例3: 手动逐日模拟 ===")

    db = DataAssimilationHDF5("data/manual_simulation.h5")
    project_name = "manual_corn_2013"

    # 第一天模拟（无前置状态）
    day1 = date(2013, 5, 1)
    print(f"模拟第1天: {day1}")

    model1 = create_model_with_initial_state(day1, day1 + timedelta(days=1))
    # result1 = model1.run()  # 实际运行

    # 模拟结果
    mock_result1 = create_mock_result(day1, lai=2.0, biomass=1200)
    state_vars1 = StateVariableExtractor.extract_all_state_variables(mock_result1)

    db.save_daily_simulation(project_name, day1, model1, mock_result1, state_vars1)

    # 第二天模拟（使用前一天的状态）
    day2 = date(2013, 5, 2)
    print(f"模拟第2天: {day2}")

    # 获取前一天的状态
    previous_state = db.get_previous_state_variables(project_name, day2)
    print(f"  前一天状态变量: {list(previous_state.keys()) if previous_state else 'None'}")

    # 应用观测数据同化
    if previous_state:
        observations = {'lai_obs': 2.2, 'soil_moisture_obs': [0.25, 0.30, 0.35]}
        assimilated_state = simple_data_assimilation(previous_state, observations)
        print(f"  应用数据同化")
    else:
        assimilated_state = None

    model2 = create_model_with_initial_state(day2, day2 + timedelta(days=1), assimilated_state)
    mock_result2 = create_mock_result(day2, lai=2.4, biomass=1400)
    state_vars2 = StateVariableExtractor.extract_all_state_variables(mock_result2)

    db.save_daily_simulation(project_name, day2, model2, mock_result2, state_vars2)

    # 查看所有模拟日期
    all_dates = db.list_simulation_dates(project_name)
    print(f"✓ 完成模拟，共 {len(all_dates)} 天: {all_dates}")


def create_mock_result(simulation_date: date, lai: float, biomass: float) -> 'Result':
    """创建模拟的结果对象用于演示"""
    from pyahc.model.result import Result
    import pandas as pd

    mock_csv = pd.DataFrame({
        'DATETIME': [datetime.combine(simulation_date, datetime.min.time())],
        'theta_1': [0.25 + np.random.normal(0, 0.02)],
        'theta_2': [0.30 + np.random.normal(0, 0.02)],
        'theta_3': [0.35 + np.random.normal(0, 0.02)],
        'lai': [lai],
        'biomass': [biomass],
        'gwl': [-150.0 + np.random.normal(0, 5)]
    })
    mock_csv.set_index('DATETIME', inplace=True)

    return Result(
        log=f"模拟日期: {simulation_date}",
        output={'csv': mock_csv},
        warning=[]
    )


if __name__ == "__main__":
    # 创建数据目录
    Path("data").mkdir(exist_ok=True)

    # 运行示例
    try:
        example_1_basic_usage()
        example_2_workflow_usage()
        example_3_manual_daily_simulation()

        print("\n🎉 所有示例运行完成！")
        print("HDF5文件已保存在 data/ 目录中")

    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
        import traceback
        traceback.print_exc()
```

### 6.2 与现有hetao_corn_2013.py集成

修改现有的 `hetao_corn_2013.py` 文件，添加HDF5支持：

```python
# 在文件开头添加导入
from pyahc.db.data_assimilation import DataAssimilationHDF5
from pyahc.utils.state_extractor import StateVariableExtractor
from pyahc.utils.data_assimilation_workflow import DataAssimilationWorkflow

def run_ahc_simulation_with_hdf5(output_dir=None,
                                use_validation=True,
                                enable_hdf5=True,
                                hdf5_path="simulation_results.h5"):
    """
    运行带HDF5支持的AHC模拟流程

    Args:
        output_dir: 输出目录
        use_validation: 是否使用验证框架
        enable_hdf5: 是否启用HDF5存储
        hdf5_path: HDF5文件路径
    """
    print(f"=== 开始 AHC-V201.exe 执行案例（HDF5支持：{enable_hdf5}）===")

    # 初始化HDF5数据库（如果启用）
    hdf5_db = None
    if enable_hdf5:
        hdf5_db = DataAssimilationHDF5(hdf5_path)
        print(f"✓ HDF5数据库初始化: {hdf5_path}")

    # 创建输出目录
    if output_dir is None:
        project_root = Path(__file__).parent
        temp_dir_path = project_root / "temp_hetao_corn_2013"
        temp_dir_path.mkdir(exist_ok=True)
        output_dir = str(temp_dir_path)

    try:
        # 1. 创建模型组件
        print("创建模型组件...")
        model, _, epic_crop, obs_points, meteo_manager = create_model_components()

        # 2. 生成输入文件
        generate_input_files(model, epic_crop, obs_points, output_dir)

        # 3. 运行 AHC 可执行文件
        success = run_ahc_executable(output_dir)

        # 4. 检查输出结果
        check_output_files(output_dir)

        if success and enable_hdf5:
            # 5. 处理HDF5存储
            print("\n处理HDF5存储...")

            # 读取模型结果
            result = read_model_results(output_dir)

            # 提取状态变量
            extractor = StateVariableExtractor()
            state_vars = extractor.extract_all_state_variables(result)

            # 保存到HDF5
            simulation_date = model.generalsettings.tstart
            hdf5_db.save_daily_simulation(
                project_name="hetao_corn_2013",
                simulation_date=simulation_date,
                model=model,
                result=result,
                state_variables=state_vars
            )

            print(f"✓ 结果已保存到HDF5: {hdf5_path}")
            print(f"  状态变量: {list(state_vars.keys())}")

        if success:
            print("\n✓ AHC 模拟执行成功！")
        else:
            print("\n✗ AHC 模拟执行失败")

        return output_dir, hdf5_db

    except Exception as e:
        print(f"\n✗ 执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return output_dir, hdf5_db


def read_model_results(output_dir: str) -> 'Result':
    """从输出目录读取模型结果"""
    from pyahc.model.result import Result
    import pandas as pd

    output_path = Path(output_dir)

    # 读取CSV输出文件
    csv_files = list(output_path.glob("rs0*output.csv"))
    csv_data = None

    if csv_files:
        try:
            csv_data = pd.read_csv(csv_files[0], comment="*", index_col=0)
            csv_data.index = pd.to_datetime(csv_data.index)
        except Exception as e:
            print(f"读取CSV文件失败: {e}")

    # 读取日志文件
    log_content = ""
    log_file = output_path / "AHC.LOG"
    if log_file.exists():
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                log_content = f.read()
        except Exception as e:
            print(f"读取日志文件失败: {e}")

    # 创建Result对象
    output_dict = {}
    if csv_data is not None:
        output_dict['csv'] = csv_data

    return Result(
        log=log_content,
        output=output_dict,
        warning=[]
    )


# 修改main函数
def main():
    """主函数 - 展示HDF5集成的使用"""
    print("=== pyAHC HDF5集成演示 ===")

    try:
        # 运行带HDF5支持的模拟
        output_directory, hdf5_db = run_ahc_simulation_with_hdf5(
            use_validation=True,
            enable_hdf5=True,
            hdf5_path="hetao_corn_simulation.h5"
        )

        if hdf5_db:
            # 展示HDF5功能
            dates = hdf5_db.list_simulation_dates("hetao_corn_2013")
            print(f"\nHDF5中的模拟日期: {dates}")

            if dates:
                # 加载第一个日期的数据
                model, result, state_vars = hdf5_db.load_daily_simulation(
                    "hetao_corn_2013", dates[0]
                )
                print(f"加载的状态变量: {list(state_vars.keys())}")

        print(f"\n案例执行完成，结果保存在: {output_directory}")
        return 0

    except Exception as e:
        print(f"✗ 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
```

## 7. 分阶段实施策略

### 7.1 阶段1：核心功能实现（4-6周）

#### 目标
- 实现基础的数据同化状态管理器
- 创建配置驱动的状态变量提取器
- 建立完整的单元测试框架

#### 具体步骤

**步骤1.1：创建项目结构**
```bash
cd pyAHC
mkdir -p pyahc/db
mkdir -p pyahc/utils
mkdir -p pyahc/config
mkdir -p examples/data_assimilation
mkdir -p tests/test_data_assimilation
mkdir -p data/configs
```

**步骤1.2：实现核心类**
```bash
# 创建核心文件
touch pyahc/db/data_assimilation_hdf5.py
touch pyahc/utils/state_extractor.py
touch pyahc/utils/data_assimilation_interface.py
touch pyahc/config/default_state_variables.yaml
```

**步骤1.3：配置文件设置**
创建 `pyahc/config/default_state_variables.yaml`：
```yaml
state_variables:
  soil_moisture:
    csv_columns: ["theta_1", "theta_2", "theta_3", "theta_4", "theta_5"]
    alternative_names: ["moisture", "theta", "swc", "soil_water_content"]
    data_type: "array"
    required: true
    default_value: [0.25, 0.30, 0.35, 0.40, 0.45]
    validation:
      min: 0.0
      max: 1.0
  lai:
    csv_columns: ["lai", "LAI", "leaf_area_index"]
    alternative_names: ["leaf_area", "canopy_cover"]
    data_type: "scalar"
    required: true
    default_value: 1.0
    validation:
      min: 0.0
      max: 10.0
```

**步骤1.4：单元测试**
创建 `tests/test_data_assimilation/test_state_extractor.py`：
```python
import pytest
import pandas as pd
from pyahc.utils.state_extractor import ConfigurableStateExtractor
from pyahc.model.result import Result

def test_soil_moisture_extraction():
    # 测试土壤含水量提取
    mock_csv = pd.DataFrame({
        'theta_1': [0.25], 'theta_2': [0.30], 'theta_3': [0.35]
    })
    result = Result(log="test", output={'csv': mock_csv}, warning=[])

    extractor = ConfigurableStateExtractor()
    state_vars = extractor.extract_all_state_variables(result)

    assert 'soil_moisture' in state_vars
    assert len(state_vars['soil_moisture']) == 3
    assert state_vars['soil_moisture'] == [0.25, 0.30, 0.35]

def test_missing_required_variable():
    # 测试缺失必需变量的处理
    mock_csv = pd.DataFrame({'other_var': [1.0]})
    result = Result(log="test", output={'csv': mock_csv}, warning=[])

    extractor = ConfigurableStateExtractor()
    state_vars = extractor.extract_all_state_variables(result, validate_required=True)

    # 应该使用默认值
    assert 'soil_moisture' in state_vars
    assert state_vars['soil_moisture'] == [0.25, 0.30, 0.35, 0.40, 0.45]
```

**步骤1.5：验收测试**
```bash
# 运行单元测试
pytest tests/test_data_assimilation/ -v

# 运行集成测试
python examples/data_assimilation/test_basic_functionality.py
```

### 7.2 阶段2：集成和优化（3-4周）

#### 目标
- 与现有项目集成
- 实现性能优化功能
- 添加数据验证和错误恢复机制

#### 具体步骤

**步骤2.1：性能优化实现**
```python
# 在 DataAssimilationHDF5 类中添加
def enable_compression(self, compression_type='gzip', compression_level=6):
    """启用数据压缩"""
    self.compression_config = {
        'compression': compression_type,
        'compression_opts': compression_level,
        'shuffle': True,
        'fletcher32': True
    }

def _save_with_compression(self, group, name, data):
    """带压缩的数据保存"""
    if self.enable_compression:
        group.create_dataset(
            name, data=data,
            chunks=True,
            **self.compression_config
        )
    else:
        group.create_dataset(name, data=data)
```

**步骤2.2：与现有项目集成**
修改 `hetao_corn_2013.py`：
```python
# 在文件开头添加
from pyahc.db.data_assimilation_hdf5 import DataAssimilationHDF5
from pyahc.utils.state_extractor import ConfigurableStateExtractor

def run_with_data_assimilation():
    # 现有代码...

    # 添加数据同化支持
    da_hdf5 = DataAssimilationHDF5("simulation_results.h5")
    extractor = ConfigurableStateExtractor()

    # 运行模型后
    result = model.run()
    state_vars = extractor.extract_all_state_variables(result)

    # 保存到HDF5
    da_hdf5.save_simulation_state(
        project_name="hetao_corn_2013",
        simulation_date=start_date,
        model=model,
        result=result,
        state_variables=state_vars
    )
```

**步骤2.3：性能基准测试**
创建 `tests/performance/benchmark_hdf5.py`：
```python
import time
import numpy as np
from pyahc.db.data_assimilation_hdf5 import DataAssimilationHDF5

def benchmark_save_performance():
    """测试保存性能"""
    da_hdf5 = DataAssimilationHDF5("benchmark.h5", enable_compression=True)

    # 生成测试数据
    large_state = {
        'soil_moisture': np.random.rand(100).tolist(),
        'lai': np.random.rand(),
        'biomass': np.random.rand()
    }

    start_time = time.time()
    for i in range(100):
        da_hdf5.save_simulation_state(
            f"project_{i}", date(2013, 5, i+1),
            mock_model, mock_result, large_state
        )
    end_time = time.time()

    print(f"保存100个状态耗时: {end_time - start_time:.3f}秒")
    assert end_time - start_time < 10.0  # 应在10秒内完成
```

### 7.3 阶段3：高级功能（2-3周）

#### 目标
- 标准化数据同化接口
- 支持多种观测数据格式
- 实现并发访问控制

#### 具体步骤

**步骤3.1：数据同化算法集成**
创建 `examples/data_assimilation/enkf_example.py`：
```python
from pyahc.utils.data_assimilation_interface import DataAssimilationInterface
import numpy as np

def run_enkf_example():
    """集合卡尔曼滤波示例"""
    da_interface = DataAssimilationInterface()

    # 准备状态变量
    state_vars = {
        'soil_moisture': [0.25, 0.30, 0.35],
        'lai': 2.5
    }

    # 准备观测数据
    observations = {
        'soil_moisture': {
            'values': [0.26, 0.31, 0.36],
            'errors': [0.02, 0.02, 0.02]
        },
        'lai': {
            'values': 2.3,
            'errors': 0.2
        }
    }

    # 创建数据同化包
    assim_package = da_interface.create_assimilation_package(
        state_vars, observations
    )

    # 调用外部EnKF算法
    assimilated_state = external_enkf_algorithm(assim_package)

    return assimilated_state

def external_enkf_algorithm(assim_package):
    """外部EnKF算法接口示例"""
    # 这里调用实际的EnKF算法
    # 返回同化后的状态
    pass
```

**步骤3.2：并发访问控制测试**
创建 `tests/concurrency/test_concurrent_access.py`：
```python
import threading
import time
from pyahc.db.data_assimilation_hdf5 import DataAssimilationHDF5

def test_concurrent_write():
    """测试并发写入"""
    da_hdf5 = DataAssimilationHDF5("concurrent_test.h5")

    def write_worker(worker_id):
        for i in range(10):
            da_hdf5.save_simulation_state(
                f"project_{worker_id}",
                date(2013, 5, i+1),
                mock_model, mock_result, mock_state
            )
            time.sleep(0.01)

    # 启动多个写入线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=write_worker, args=(i,))
        threads.append(thread)
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    # 验证数据完整性
    assert da_hdf5.verify_data_integrity()
```

### 7.4 验收和部署

#### 最终验收标准
1. **功能完整性测试**：所有核心功能按需求实现
2. **性能基准测试**：单日状态读写操作在1秒内完成
3. **集成测试**：与现有pyAHC组件无缝集成
4. **并发测试**：支持多进程并发访问
5. **数据完整性测试**：异常情况下数据不损坏

#### 部署检查清单
- [ ] 所有单元测试通过
- [ ] 性能基准测试达标
- [ ] 集成测试通过
- [ ] 文档完整且准确
- [ ] 示例代码可运行
- [ ] 错误处理机制完善
- [ ] 日志记录详细

## 8. 风险缓解和注意事项

### 8.1 技术风险缓解

#### 风险1：数据兼容性问题
**风险描述**：新旧数据格式不兼容，导致历史数据无法访问

**缓解策略**：
```python
class DataMigrationTool:
    """数据迁移工具"""

    def migrate_legacy_hdf5(self, old_file: str, new_file: str):
        """迁移旧版HDF5文件到新格式"""
        with h5py.File(old_file, 'r') as old_f, h5py.File(new_file, 'w') as new_f:
            # 检测旧格式版本
            version = old_f.attrs.get('version', '0.0.0')

            if version < '1.0.0':
                self._migrate_from_legacy(old_f, new_f)
            else:
                self._copy_compatible_data(old_f, new_f)

    def verify_migration(self, old_file: str, new_file: str) -> bool:
        """验证迁移结果"""
        # 实现数据完整性检查
        pass
```

#### 风险2：性能瓶颈
**风险描述**：大量数据读写影响系统性能

**缓解策略**：
1. **分块存储**：
```python
def _save_large_array(self, group, name, data):
    """分块保存大型数组"""
    chunk_size = min(self.chunk_size, len(data))
    group.create_dataset(
        name, data=data,
        chunks=(chunk_size,),
        compression='gzip',
        compression_opts=6
    )
```

2. **异步I/O**：
```python
import asyncio
import aiofiles

async def async_save_state(self, project_name, simulation_date, state_data):
    """异步保存状态数据"""
    async with self._async_file_lock:
        # 异步保存实现
        pass
```

3. **内存管理**：
```python
def _optimize_memory_usage(self):
    """优化内存使用"""
    # 定期清理缓存
    if len(self._cache) > self.max_cache_size:
        self._cache.clear()

    # 使用内存映射
    import mmap
    # 实现内存映射访问
```

#### 风险3：数据损坏
**风险描述**：系统异常导致HDF5文件损坏

**缓解策略**：
```python
class DataIntegrityManager:
    """数据完整性管理器"""

    def create_checkpoint(self, file_path: str) -> str:
        """创建数据检查点"""
        checkpoint_path = f"{file_path}.checkpoint"
        shutil.copy2(file_path, checkpoint_path)
        return checkpoint_path

    def verify_file_integrity(self, file_path: str) -> bool:
        """验证文件完整性"""
        try:
            with h5py.File(file_path, 'r') as f:
                # 检查文件结构
                self._check_file_structure(f)
                # 验证数据一致性
                self._verify_data_consistency(f)
                return True
        except Exception as e:
            logger.error(f"文件完整性检查失败: {e}")
            return False

    def repair_corrupted_file(self, file_path: str, backup_path: str):
        """修复损坏的文件"""
        if self.verify_file_integrity(backup_path):
            shutil.copy2(backup_path, file_path)
            logger.info("已从备份恢复文件")
        else:
            raise RuntimeError("备份文件也已损坏")
```

### 8.2 性能优化策略

#### 策略1：智能缓存
```python
from functools import lru_cache
import weakref

class SmartCache:
    """智能缓存管理器"""

    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self._cache = {}
        self._access_times = {}

    @lru_cache(maxsize=128)
    def get_state_variables(self, project_name: str, date_str: str):
        """缓存状态变量访问"""
        cache_key = f"{project_name}_{date_str}"

        if cache_key in self._cache:
            self._access_times[cache_key] = time.time()
            return self._cache[cache_key]

        # 从文件加载
        data = self._load_from_file(project_name, date_str)
        self._cache[cache_key] = data
        self._access_times[cache_key] = time.time()

        # 清理过期缓存
        self._cleanup_cache()

        return data
```

#### 策略2：并行处理
```python
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

class ParallelProcessor:
    """并行处理器"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers

    def parallel_state_extraction(self, results: List[Result]) -> List[Dict]:
        """并行提取状态变量"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [
                executor.submit(self.extractor.extract_all_state_variables, result)
                for result in results
            ]
            return [future.result() for future in futures]

    def parallel_data_assimilation(self, state_packages: List[Dict]) -> List[Dict]:
        """并行数据同化"""
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [
                executor.submit(self._run_assimilation, package)
                for package in state_packages
            ]
            return [future.result() for future in futures]
```

### 8.3 维护和监控

#### 监控指标
```python
class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics = {
            'read_times': [],
            'write_times': [],
            'file_sizes': [],
            'error_counts': 0,
            'cache_hit_rate': 0.0
        }

    def log_operation(self, operation: str, duration: float, success: bool):
        """记录操作指标"""
        if operation == 'read':
            self.metrics['read_times'].append(duration)
        elif operation == 'write':
            self.metrics['write_times'].append(duration)

        if not success:
            self.metrics['error_counts'] += 1

    def generate_report(self) -> Dict:
        """生成性能报告"""
        return {
            'avg_read_time': np.mean(self.metrics['read_times']),
            'avg_write_time': np.mean(self.metrics['write_times']),
            'error_rate': self.metrics['error_counts'] / len(self.metrics['read_times']),
            'cache_efficiency': self.metrics['cache_hit_rate']
        }
```

### 8.4 部署和运维注意事项

#### 部署检查清单
1. **环境准备**
   - [ ] Python 3.11+ 环境
   - [ ] h5py ^3.11.0 已安装
   - [ ] 足够的磁盘空间（建议至少10GB）
   - [ ] 适当的文件权限设置

2. **配置验证**
   - [ ] 状态变量配置文件正确
   - [ ] 数据同化参数合理
   - [ ] 日志级别适当设置
   - [ ] 备份策略已配置

3. **性能调优**
   - [ ] 压缩参数优化
   - [ ] 缓存大小合理
   - [ ] 并发参数调整
   - [ ] 内存限制设置

#### 运维监控
```python
# 定期健康检查脚本
def health_check():
    """系统健康检查"""
    checks = {
        'disk_space': check_disk_space(),
        'file_integrity': check_file_integrity(),
        'performance': check_performance_metrics(),
        'memory_usage': check_memory_usage()
    }

    for check_name, result in checks.items():
        if not result['healthy']:
            send_alert(f"{check_name} 检查失败: {result['message']}")

    return all(check['healthy'] for check in checks.values())
```

#### 数据备份策略
```python
class BackupManager:
    """备份管理器"""

    def __init__(self, backup_dir: str, retention_days: int = 30):
        self.backup_dir = Path(backup_dir)
        self.retention_days = retention_days

    def create_backup(self, source_file: str):
        """创建备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{timestamp}.h5"
        backup_path = self.backup_dir / backup_name

        shutil.copy2(source_file, backup_path)
        logger.info(f"备份已创建: {backup_path}")

        # 清理过期备份
        self._cleanup_old_backups()

    def _cleanup_old_backups(self):
        """清理过期备份"""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)

        for backup_file in self.backup_dir.glob("backup_*.h5"):
            if backup_file.stat().st_mtime < cutoff_date.timestamp():
                backup_file.unlink()
                logger.info(f"已删除过期备份: {backup_file}")
```

### 8.5 故障排除指南

#### 常见问题及解决方案

1. **HDF5文件损坏**
   ```bash
   # 检查文件完整性
   h5dump -H corrupted_file.h5

   # 尝试修复
   h5repack corrupted_file.h5 repaired_file.h5
   ```

2. **性能问题**
   ```python
   # 启用性能分析
   import cProfile
   cProfile.run('your_function()', 'profile_output.prof')

   # 分析结果
   import pstats
   stats = pstats.Stats('profile_output.prof')
   stats.sort_stats('cumulative').print_stats(10)
   ```

3. **内存泄漏**
   ```python
   # 使用内存分析工具
   import tracemalloc
   tracemalloc.start()

   # 运行代码
   your_function()

   # 检查内存使用
   current, peak = tracemalloc.get_traced_memory()
   print(f"当前内存: {current / 1024 / 1024:.1f} MB")
   print(f"峰值内存: {peak / 1024 / 1024:.1f} MB")
   ```

这套完整的HDF5数据同化集成方案提供了科学合理的架构设计、健壮的实现方案和全面的风险缓解策略，确保pyAHC项目能够成功集成数据同化功能。

